#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kitaev_model运行器模块
提供了一种更面向对象的方式来进行Kitaev模型模拟
"""

import os
import sys
import time
import numpy as np
import jax
import jax.numpy as jnp
import netket as nk
import netket.optimizer as nk_opt
import flax
from datetime import datetime

# 添加模型分片必要的导入
from jax.sharding import Mesh, PartitionSpec as P, NamedSharding
from jax.experimental import mesh_utils
from jax.lax import with_sharding_constraint

from src.utils.FE_VMC_SR import CustomFreeEnergyVMC_SR
from src.utils.logging import log_message
from src.physics.kitaev import (
    create_honeycomb_lattice, 
    create_kitaev_hamiltonian, 
    get_symmetries,
    save_lattice_figure
)

class KitaevRunner:
    """Kitaev模型运行器类，提供面向对象的接口"""
    
    def __init__(self, Lx, <PERSON><PERSON>, <PERSON>x, <PERSON><PERSON>, Kz, J, hx, hy, hz, <PERSON><PERSON>, 
                 model_type=None, model_class=None, model_config=None, training_config=None, 
                 reference_energy=None, use_model_sharding=True, custom_mesh_shape=None,
                 output_dir=None, checkpoint_config=None):
        """
        初始化运行器
        
        Args:
            Lx, Ly: 晶格尺寸
            Kx, Ky, Kz: Kitaev相互作用强度
            J: Heisenberg相互作用强度
            hx, hy, hz: [111]方向磁场
            Lambda: plaquette算符的拉格朗日乘子
            model_type: 模型类型 ("cRBM" 或 "ViT")
            model_class: 模型类
            model_config: 模型配置类
            training_config: 训练配置类
            reference_energy: 参考能量，用于显示相对误差
            use_model_sharding: 是否启用模型分片
            custom_mesh_shape: 自定义GPU网格形状，格式为(model_axis, batch_axis)，None表示自动配置
            output_dir: Hydra管理的输出目录。如果提供，将覆盖默认的目录结构。
            checkpoint_config: checkpoint配置字典
        """
        self.Lx = Lx
        self.Ly = Ly
        self.Kx = Kx
        self.Ky = Ky
        self.Kz = Kz
        self.J = J
        self.hx = hx
        self.hy = hy
        self.hz = hz
        self.Lambda = Lambda
        self.reference_energy = reference_energy
        self.use_model_sharding = use_model_sharding
        self.custom_mesh_shape = custom_mesh_shape
        
        # 初始化分片相关属性
        self.mesh = None
        self.model_sharding = None
        
        # 存储模型相关信息
        self.model_type = model_type or "cRBM"
        self.model_class = model_class
        
        # 处理配置（可以是字典、类或实例）
        self.model_config = self._process_config(model_config, 'model')
        self.training_config = self._process_config(training_config, 'training')
        
        # 如果没有提供模型类，使用默认的cRBM
        if self.model_class is None:
            from src.models.cRBM import cRBM
            self.model_class = cRBM
            self.model_type = "cRBM"
        
        # 设置输出目录
        self.output_dir = output_dir or self._create_output_dir()
        
        # 使用hydra自动生成的日志文件（train.log）
        self.energy_log = os.path.join(self.output_dir, "train.log")
        
        # 初始化checkpoint配置
        self.checkpoint_config = checkpoint_config or {}
        self.checkpoint_dir = os.path.join(self.output_dir, "checkpoints")
        if self.checkpoint_config.get('enable', False):
            os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # 初始化物理系统
        self.lattice = create_honeycomb_lattice(Lx, Ly)
        self.N = self.lattice.n_nodes
    
    def _process_config(self, config, config_type):
        """
        处理配置，统一转换为支持属性访问的对象
        
        Args:
            config: 配置，可以是字典、类或实例
            config_type: 配置类型 ('model' 或 'training')
        
        Returns:
            支持属性访问的配置对象
        """
        if config is None:
            # 如果没有提供配置，创建默认配置对象
            if config_type == 'model':
                return self._dict_to_object({
                    'alpha': 4,
                    'param_dtype': 'complex128',
                    'use_hidden_bias': True,
                    'use_visible_bias': True,
                    'use_symmetries': True
                })
            else:
                return self._dict_to_object({
                    'learning_rate': 0.01,
                    'n_cycles': 1,
                    'initial_period': 100,
                    'period_mult': 2.0,
                    'max_temperature': 1.0,
                    'min_temperature': 0.0,
                    'n_samples': 1024,
                    'n_discard_per_chain': 0,
                    'chunk_size': 256,
                    'diag_shift': 0.01,
                    'grad_clip': 1.0
                })
        
        if isinstance(config, dict):
            # 如果是字典，转换为支持属性访问的对象
            return self._dict_to_object(config)
        elif isinstance(config, type):
            # 如果是类，创建实例
            return config()
        else:
            # 如果已经是实例，直接使用
            return config
    
    def _dict_to_object(self, config_dict):
        """将字典转换为支持属性访问的对象"""
        class ConfigObject:
            def __init__(self, config_dict):
                for key, value in config_dict.items():
                    setattr(self, key, value)
            
            def get_total_iterations(self):
                """计算总迭代次数（用于训练配置）"""
                try:
                    n_cycles = getattr(self, 'n_cycles', 8)
                    initial_period = getattr(self, 'initial_period', 100)
                    period_mult = getattr(self, 'period_mult', 2.0)
                    
                    total_iters = 0
                    current_period = initial_period
                    for cycle in range(n_cycles):
                        total_iters += int(current_period * (period_mult ** cycle))
                    return total_iters
                except Exception:
                    return 0
        
        return ConfigObject(config_dict)
    
    def _create_output_dir(self):
        """创建默认输出目录"""
        # 创建基于物理系统参数的输出目录
        output_dir = f"results/L{self.Lx}x{self.Ly}_K{self.Kx}.{self.Ky}.{self.Kz}_J{self.J}_h{self.hx}.{self.hy}.{self.hz}_L{self.Lambda}"
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def _setup_model_sharding(self):
        """设置JAX模型分片"""
        num_devices = len(jax.devices())
        mesh_shape = None
        
        # 优先使用自定义网格形状
        if self.custom_mesh_shape is not None:
            model_axis, batch_axis = self.custom_mesh_shape
            # 检查提供的网格形状是否与可用设备数匹配
            if model_axis * batch_axis == num_devices:
                mesh_shape = self.custom_mesh_shape
            else:
                pass  # Silently use auto config
        
        # 如果没有有效的自定义网格形状，则使用自动配置
        if mesh_shape is None:
            if num_devices >= 4 and num_devices % 2 == 0:
                # 对于偶数个设备（>=4），优先模型分片
                mesh_shape = (2, num_devices // 2)
            elif num_devices == 2:
                # 对于2个设备，优先模型分片
                mesh_shape = (2, 1)
            else:
                # 对于奇数个或单个设备，不进行模型分片
                mesh_shape = (1, num_devices)
        
        axis_names = ('model', 'batch')
        device_mesh = mesh_utils.create_device_mesh(mesh_shape)
        self.mesh = Mesh(devices=device_mesh, axis_names=axis_names)
        
        # 创建模型分片策略
        self.model_sharding = NamedSharding(self.mesh, P('model', None))
        self.batch_sharding = NamedSharding(self.mesh, P(None, 'batch'))
    
    def _get_smart_sharding_spec(self, shape):
        """
        根据参数形状和设备网格智能选择分片策略
        """
        mesh_shape = self.mesh.devices.shape  # e.g., (4, 1) for model_axis=4, batch_axis=1
        model_size, batch_size = mesh_shape
        
        if len(shape) == 1:
            # 1维参数（如偏置）
            if shape[0] % model_size == 0:
                return P('model')  # 沿model轴分片
            else:
                return P()  # 复制
        
        elif len(shape) == 2:
            # 2维参数（如权重矩阵）
            dim0_divisible = shape[0] % model_size == 0
            dim1_divisible = shape[1] % model_size == 0
            
            if dim1_divisible:
                return P(None, 'model')  # 第二维按model轴分片
            elif dim0_divisible:
                return P('model', None)  # 第一维按model轴分片
            else:
                return P()  # 都不能分片，则复制
                
        elif len(shape) >= 3:
            # 多维参数（如卷积核或注意力参数）
            # 优先分片最后一个维度 (通常是输出通道/特征)
            if shape[-1] % model_size == 0:
                spec_list = [None] * (len(shape) - 1) + ['model']
                return P(*spec_list)
            # 其次分片倒数第二个维度
            elif shape[-2] % model_size == 0:
                spec_list = [None] * (len(shape) - 2) + ['model', None]
                return P(*spec_list)
            # 新增：最后尝试分片第一个维度（适配注意力头等场景）
            elif shape[0] % model_size == 0:
                spec_list = ['model'] + [None] * (len(shape) - 1)
                return P(*spec_list)
            else:
                # 所有维度都无法整除，则复制
                return P()
        
        else:
            return P()  # 标量，复制
    
    def setup_model(self):
        """设置模型和优化器"""
        # 检查是否需要从checkpoint恢复
        checkpoint_data = None
        if self.checkpoint_config.get('resume_from'):
            checkpoint_data = self._load_checkpoint()
        
        # 设置模型分片（现在energy_log已经初始化）
        if self.use_model_sharding and len(jax.devices()) > 1:
            self._setup_model_sharding()
            
        # 创建Hamiltonian
        self.H, self.hi = create_kitaev_hamiltonian(
            self.lattice, self.Kx, self.Ky, self.Kz, self.J, 
            self.hx, self.hy, self.hz, self.Lambda
        )
        
        # 设置采样器
        self.sampler = nk.sampler.MetropolisLocal(
            hilbert=self.hi,
            n_chains=self.training_config.n_samples
        )
        
        # 根据模型类型设置模型
        if self.model_type.lower() == "crbm":
            model_no_symm = self.model_class(
                Lx=self.Lx,
                Ly=self.Ly,
                alpha=self.model_config.alpha,
                param_dtype=getattr(np, self.model_config.param_dtype),
                use_hidden_bias=self.model_config.use_hidden_bias,
                use_visible_bias=self.model_config.use_visible_bias
            )
        elif self.model_type.lower() == "vit":
            model_no_symm = self.model_class(
                num_layers=self.model_config.num_layers,
                d_model=self.model_config.d_model,
                heads=self.model_config.heads,
                patch_size=self.model_config.patch_size,
                n_sites=self.N,
                param_dtype=getattr(jnp, self.model_config.param_dtype)
            )

        elif self.model_type.lower() == "ctwf":
            model_no_symm = self.model_class(
                num_layers=self.model_config.num_layers,
                d_model=self.model_config.d_model,
                heads=self.model_config.heads,
                patch_size=self.model_config.patch_size,
                n_sites=self.N,
                param_dtype=getattr(jnp, self.model_config.param_dtype)
            )
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
        
        # 获取对称性
        symmetries = get_symmetries(self.lattice)
        
        # 根据ModelConfig中的use_symmetries参数决定是否使用对称性
        if self.model_config.use_symmetries:
            # 使用对称性
            self.model = nk.nn.blocks.SymmExpSum(
                module=model_no_symm, 
                symm_group=symmetries, 
                character_id=0
            )
            symmetries_used = len(symmetries)
        else:
            # 不使用对称性，直接使用原始模型
            self.model = model_no_symm
            symmetries_used = 0
        
        # 创建变分量子态
        # 注意：模型分片时不应该减少样本数，这会降低训练质量
        n_samples = self.training_config.n_samples
        chunk_size = self.training_config.chunk_size
        
        self.vqs = nk.vqs.MCState(
            sampler=self.sampler,
            model=self.model,
            n_samples=n_samples,
            n_discard_per_chain=self.training_config.n_discard_per_chain,
            chunk_size=chunk_size,
        )
        
        # 应用模型分片约束到变分状态参数
        if self.use_model_sharding and self.mesh is not None:
            # 应用分片约束到模型参数（静默执行）
            current_params = self.vqs.parameters
            
            with self.mesh:
                def shard_params(params):
                    if isinstance(params, dict):
                        sharded_params = {}
                        for key, value in params.items():
                            if isinstance(value, dict):
                                sharded_params[key] = shard_params(value)
                            else:
                                sharding_spec = self._get_smart_sharding_spec(value.shape)
                                param_sharding = NamedSharding(self.mesh, sharding_spec)
                                sharded_params[key] = jax.device_put(value, param_sharding)
                        return sharded_params
                    else:
                        return params
                
                sharded_params = shard_params(current_params)
                self.vqs._params = sharded_params
        
        # 从checkpoint恢复参数（如果有）
        if checkpoint_data is not None:
            # 直接设置参数，不使用replace方法
            self.vqs.parameters = checkpoint_data['parameters']
            log_message(self.energy_log, "✓ 变分状态参数已从checkpoint恢复")
            # 保存起始迭代次数以便后续使用
            self.start_iteration = checkpoint_data['iteration']
        else:
            self.start_iteration = 0
        
        return self
    
    def _count_model_parameters(self):
        """计算模型总参数数量"""
        param_count = 0
        for param in jax.tree_leaves(self.vqs.parameters):
            param_count += param.size
        return param_count
    
    def _log_training_info(self, n_iter):
        """记录训练开始前的详细信息"""
        # 获取物理模型名称
        physics_model_name = "Kitaev Model"  # 可以根据需要从配置中获取
        
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"{self.model_type} for {physics_model_name}")
        log_message(self.energy_log, "="*50)
        
        # 系统参数
        log_message(self.energy_log, "System parameters:")
        log_message(self.energy_log, f"  - System size: L=({self.Lx},{self.Ly}), N={self.N}")
        log_message(self.energy_log, f"  - Kitaev coupling: Kx=({self.Kx}, {self.Ky}, {self.Kz})")
        log_message(self.energy_log, f"  - Heisenberg coupling: J={self.J}")
        log_message(self.energy_log, f"  - Magnetic field: h=({self.hx}, {self.hy}, {self.hz})")
        log_message(self.energy_log, f"  - Lambda parameter: {self.Lambda}")
        if self.reference_energy is not None:
            log_message(self.energy_log, f"  - Reference energy: {self.reference_energy}")
        
        # 模型参数
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Model parameters:")
        if self.model_type.lower() == "crbm":
            log_message(self.energy_log, f"  - Alpha = {self.model_config.alpha}")
            log_message(self.energy_log, f"  - Parameter dtype = {self.model_config.param_dtype}")
            log_message(self.energy_log, f"  - Use hidden bias = {self.model_config.use_hidden_bias}")
            log_message(self.energy_log, f"  - Use visible bias = {self.model_config.use_visible_bias}")
        elif self.model_type.lower() in ["vit", "ctwf"]:
            log_message(self.energy_log, f"  - Number of layers = {self.model_config.num_layers}")
            log_message(self.energy_log, f"  - Model dimension = {self.model_config.d_model}")
            log_message(self.energy_log, f"  - Number of heads = {self.model_config.heads}")
            log_message(self.energy_log, f"  - Patch size = {self.model_config.patch_size}")
            log_message(self.energy_log, f"  - Parameter dtype = {self.model_config.param_dtype}")
        
        # 获取对称性信息
        symmetries = get_symmetries(self.lattice)
        log_message(self.energy_log, f"  - Use symmetries = {self.model_config.use_symmetries}")
        if self.model_config.use_symmetries:
            log_message(self.energy_log, f"  - Number of symmetries = {len(symmetries)}")
        
        # 计算并显示总参数数
        total_params = self._count_model_parameters()
        log_message(self.energy_log, f"  - Total parameters = {total_params}")
        
        # 训练参数
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Training parameters:")
        log_message(self.energy_log, f"  - Learning rate: {self.training_config.learning_rate}")
        log_message(self.energy_log, f"  - Total iterations: {n_iter}")
        log_message(self.energy_log, f"  - Annealing cycles: {getattr(self.training_config, 'n_cycles', 1)}")
        log_message(self.energy_log, f"  - Initial period: {getattr(self.training_config, 'initial_period', 100)}")
        log_message(self.energy_log, f"  - Period multiplier: {getattr(self.training_config, 'period_mult', 2.0)}")
        log_message(self.energy_log, f"  - Temperature range: {getattr(self.training_config, 'min_temperature', 0.0)}-{getattr(self.training_config, 'max_temperature', 1.0)}")
        log_message(self.energy_log, f"  - Samples: {self.training_config.n_samples}")
        log_message(self.energy_log, f"  - Discarded samples: {getattr(self.training_config, 'n_discard_per_chain', 0)}")
        log_message(self.energy_log, f"  - Chunk size: {self.training_config.chunk_size}")
        log_message(self.energy_log, f"  - Diagonal shift: {getattr(self.training_config, 'diag_shift', 0.01)}")
        log_message(self.energy_log, f"  - Gradient clipping: {getattr(self.training_config, 'grad_clip', 1.0)}")
        
        # Checkpoint状态
        if self.checkpoint_config.get('enable', False):
            log_message(self.energy_log, f"  - Checkpoint enabled: interval={self.checkpoint_config.get('save_interval', 500)}")
            log_message(self.energy_log, f"  - Checkpoint directory: {os.path.relpath(self.checkpoint_dir)}")
            if hasattr(self, 'start_iteration') and self.start_iteration > 0:
                log_message(self.energy_log, f"  - Resuming from iteration: {self.start_iteration}")
        else:
            log_message(self.energy_log, f"  - Checkpoint disabled")
        
        # 设备状态
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Device status:")
        
        # 获取GPU信息
        devices = jax.devices()
        num_devices = len(devices)
        
        # 尝试获取GPU型号
        try:
            # 获取第一个GPU设备的平台信息
            if devices and devices[0].platform == 'gpu':
                device_info = str(devices[0].device_kind)
                # 从设备信息中提取GPU型号，通常包含在device_kind中
                if 'A100' in device_info.upper():
                    gpu_model = 'A100'
                elif 'V100' in device_info.upper():
                    gpu_model = 'V100'
                elif 'T4' in device_info.upper():
                    gpu_model = 'T4'
                elif 'RTX' in device_info.upper():
                    gpu_model = 'RTX'
                else:
                    # 如果无法识别具体型号，显示原始信息
                    gpu_model = device_info
            else:
                gpu_model = 'Unknown'
        except:
            gpu_model = 'Unknown'
            
        log_message(self.energy_log, f"  - Devices model: {gpu_model}")
        log_message(self.energy_log, f"  - Number of devices: {num_devices}")
        log_message(self.energy_log, f"  - Sharding: {self.use_model_sharding}")
        if self.mesh is not None:
            # 格式化mesh shape为列表格式
            mesh_shape_list = [('model', self.mesh.shape['model']), ('batch', self.mesh.shape['batch'])]
            log_message(self.energy_log, f"  - Mesh shape: {mesh_shape_list}")
        
        log_message(self.energy_log, "="*60)

        return self
    
    def run(self, n_cycles=None, n_train=None):
        """
        运行模拟
        
        Args:
            n_cycles: 热重启周期数，如果为None则使用配置中的值
            n_train: 每次迭代的训练步数，如果为None则使用配置中的值
        """
        if n_cycles is None:
            n_cycles = getattr(self.training_config, 'n_cycles', 1)
        
        # 决定是运行训练还是仅评估
        if n_train is None:
            n_train = getattr(self.training_config, 'iterations', 5000)

        # 如果n_train为0, 则只进行评估
        if n_train == 0:
            log_message(self.energy_log, "检测到训练迭代次数为0，将仅执行评估。")
            # 评估最终能量
            final_energy = self.vqs.expect(self.H)
            log_message(self.energy_log, f"评估能量: E = {final_energy.mean} ± {final_energy.error_of_mean}")
            
            # 评估结果已记录在训练日志中，无需额外文件
        else:
            # 运行完整的训练流程
            self._run_training(n_cycles, n_train)
        
        return self
    
    def _run_training(self, n_cycles, n_train):
        """统一的训练流程"""
        # 计算总迭代次数
        n_iter = self.training_config.get_total_iterations()
        
        # 记录详细的训练信息
        self._log_training_info(n_iter)
        
        # 记录时间
        start = time.time()
        
        # 创建优化器
        optimizer = nk_opt.Sgd(learning_rate=self.training_config.learning_rate)
        
        # 准备checkpoint回调
        checkpoint_callback = None
        checkpoint_interval = 0
        if self.checkpoint_config.get('enable', False):
            checkpoint_callback = lambda iter_num, energy_mean, energy_error: self._save_checkpoint(
                self.start_iteration + iter_num, energy_mean, energy_error
            )
            checkpoint_interval = self.checkpoint_config.get('save_interval', 500)
        
        # 使用新的 CustomFreeEnergyVMC_SR 方法，传递热重启参数
        clip_norm = getattr(self.training_config, 'grad_clip', 1.0)
        vmc = CustomFreeEnergyVMC_SR(
            reference_energy=self.reference_energy,
            initial_period=self.training_config.initial_period,
            period_mult=self.training_config.period_mult,
            max_temperature=self.training_config.max_temperature,
            min_temperature=self.training_config.min_temperature,
            clip_norm=clip_norm,
            checkpoint_callback=checkpoint_callback,
            checkpoint_interval=checkpoint_interval,
            hamiltonian=self.H,
            optimizer=optimizer,
            diag_shift=self.training_config.diag_shift,
            variational_state=self.vqs
        )
        
        # 运行优化
        if self.mesh is not None:
            with self.mesh:
                vmc.run(n_iter=n_iter, energy_log=self.energy_log)
        else:
            vmc.run(n_iter=n_iter, energy_log=self.energy_log)
        
        end = time.time()
        
        runtime = end - start
        log_message(self.energy_log, "="*60)
        log_message(self.energy_log, f"Training completed | Runtime: {runtime:.1f}s")
        
        # 保存最终状态
        self._save_final_state()
    
    def _save_checkpoint(self, iteration, energy_mean=None, energy_error=None):
        """保存checkpoint到checkpoints子目录"""
        if not self.checkpoint_config.get('enable', False):
            return
        
        import pickle
        
        # 创建checkpoint数据
        # 确保energy值可以被pickle序列化（保持JAX数组用于模型恢复）
        energy_data = None
        if energy_mean is not None:
            energy_data = {'mean': energy_mean, 'error': energy_error}
        
        checkpoint_data = {
            'iteration': iteration,
            'parameters': self.vqs.parameters,
            'model_type': self.model_type,
            'model_config': self.model_config.__dict__ if hasattr(self.model_config, '__dict__') else vars(self.model_config),
            'training_config': self.training_config.__dict__ if hasattr(self.training_config, '__dict__') else vars(self.training_config),
            'system_config': {
                'Lx': self.Lx, 'Ly': self.Ly,
                'Kx': self.Kx, 'Ky': self.Ky, 'Kz': self.Kz,
                'J': self.J, 'hx': self.hx, 'hy': self.hy, 'hz': self.hz,
                'Lambda': self.Lambda
            },
            'energy': energy_data,
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存checkpoint文件
        if self.checkpoint_config.get('keep_history', True):
            checkpoint_file = os.path.join(self.checkpoint_dir, f"checkpoint_iter_{iteration:06d}.pkl")
        else:
            checkpoint_file = os.path.join(self.checkpoint_dir, "latest_checkpoint.pkl")
        
        with open(checkpoint_file, "wb") as f:
            pickle.dump(checkpoint_data, f)
        
        log_message(self.energy_log, f"✓ Checkpoint saved: {os.path.basename(checkpoint_file)}")
    
    def _load_checkpoint(self, checkpoint_path=None):
        """从checkpoint恢复训练状态"""
        import pickle
        
        # 确定checkpoint路径
        if checkpoint_path is None:
            checkpoint_path = self.checkpoint_config.get('resume_from')
        
        if checkpoint_path is None:
            return None
        
        # 如果是相对路径，相对于当前工作目录解析
        if not os.path.isabs(checkpoint_path):
            checkpoint_path = os.path.abspath(checkpoint_path)
        
        # 如果路径是目录，查找最新的checkpoint
        if os.path.isdir(checkpoint_path):
            # 直接查找最新的checkpoint文件
            checkpoint_files = [f for f in os.listdir(checkpoint_path) if f.endswith('.pkl')]
            if not checkpoint_files:
                log_message(self.energy_log, f"⚠️  在目录 {checkpoint_path} 中未找到checkpoint文件")
                return None
            checkpoint_files.sort()
            checkpoint_file = os.path.join(checkpoint_path, checkpoint_files[-1])
        else:
            checkpoint_file = checkpoint_path
        
        if not os.path.exists(checkpoint_file):
            log_message(self.energy_log, f"⚠️  Checkpoint文件不存在: {checkpoint_file}")
            return None
        
        try:
            with open(checkpoint_file, "rb") as f:
                checkpoint_data = pickle.load(f)
            
            log_message(self.energy_log, f"✓ 从checkpoint恢复: {os.path.basename(checkpoint_file)}")
            log_message(self.energy_log, f"  - 迭代次数: {checkpoint_data['iteration']}")
            if checkpoint_data.get('energy'):
                energy = checkpoint_data['energy']
                log_message(self.energy_log, f"  - 能量: {energy['mean']:.6f} ± {energy['error']:.6f}")
            log_message(self.energy_log, f"  - 时间戳: {checkpoint_data['timestamp']}")
            
            return checkpoint_data
            
        except Exception as e:
            log_message(self.energy_log, f"⚠️  加载checkpoint失败: {e}")
            return None

    def _save_final_state(self):
        """保存最终状态"""
        import pickle
        
        # 如果启用了checkpoint，保存到checkpoint目录，否则保存到Job目录
        if self.checkpoint_config.get('enable', False):
            # 保存到checkpoint目录
            state_file = os.path.join(self.checkpoint_dir, f"final_{self.model_type}.pkl")
            
            # 保存完整的checkpoint数据作为最终状态
            final_energy = self.vqs.expect(self.H)
            checkpoint_data = {
                'iteration': 'final',
                'parameters': self.vqs.parameters,
                'model_type': self.model_type,
                'model_config': self.model_config.__dict__ if hasattr(self.model_config, '__dict__') else vars(self.model_config),
                'training_config': self.training_config.__dict__ if hasattr(self.training_config, '__dict__') else vars(self.training_config),
                'system_config': {
                    'Lx': self.Lx, 'Ly': self.Ly,
                    'Kx': self.Kx, 'Ky': self.Ky, 'Kz': self.Kz,
                    'J': self.J, 'hx': self.hx, 'hy': self.hy, 'hz': self.hz,
                    'Lambda': self.Lambda
                },
                'energy': {'mean': final_energy.mean, 'error': final_energy.error_of_mean},
                'timestamp': datetime.now().isoformat(),
                'is_final': True
            }
            
            with open(state_file, "wb") as f_state:
                pickle.dump(checkpoint_data, f_state)
                
            log_message(self.energy_log, f"✓ Final state saved: checkpoints/{os.path.basename(state_file)}")
        else:
            # 原有逻辑：直接保存到Job目录
            state_file = os.path.join(self.output_dir, f"{self.model_type}.pkl")
            with open(state_file, "wb") as f_state:
                pickle.dump(self.vqs.parameters, f_state)
            log_message(self.energy_log, f"✓ Model saved: {os.path.basename(state_file)}")
        
        log_message(self.energy_log, "="*60)
    
    @classmethod
    def run_simulation(cls, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda):
        """
        静态方法：运行单个模拟 (向后兼容)
        
        Args:
            所有Kitaev模型参数
        """
        runner = cls(Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda)
        runner.setup_model()
        runner.run()
        return runner 