#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kitaev模型统一训练脚本 (Hydra-based)
"""
import os
# 在导入jax之前设置环境变量
os.environ["XLA_FLAGS"] = os.environ.get("XLA_FLAGS", "--xla_gpu_cuda_data_dir=/usr/local/cuda")
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"

import hydra
from omegaconf import DictConfig, OmegaConf, ListConfig

from src.models import cRBM, ViTFNQS, CTWFNQS
from src.runner import KitaevRunner
from src.physics.kitaev import create_honeycomb_lattice, save_lattice_figure


# 将模型名称映射到其类（不再依赖硬编码的配置类）
MODEL_REGISTRY = {
    "crbm": cRBM,
    "vit": ViTFNQS,
    "ctwf": CTWFNQS,
}

@hydra.main(config_path="../configs", config_name="config", version_base=None)
def main(cfg: DictConfig) -> None:
    """
    Hydra驱动的主函数，所有参数从配置对象cfg中获取
    """
    # ==================== 确认工作目录 ====================
    current_wd = os.getcwd()
    hydra_output_dir = hydra.core.hydra_config.HydraConfig.get().runtime.output_dir
    print(f"当前工作目录: {current_wd}")
    print(f"Hydra运行目录: {hydra_output_dir}")
    # ====================================================
    
    # ==================== 必要文件生成 ====================
    # 仅保留晶格结构图，其他配置信息已由Hydra标准文件提供
    try:
        lattice = create_honeycomb_lattice(cfg.system.Lx, cfg.system.Ly)
        lattice_fig_path = os.path.join(hydra_output_dir, "Honeycomb_lattice.png")
        save_lattice_figure(lattice, lattice_fig_path)
        print(f"✓ 晶格结构图已保存")
    except Exception as e:
        print(f"⚠️  晶格结构图保存失败: {e}")
    # ========================================================

    # 所有配置信息已由Hydra标准文件提供（.hydra/目录下）
    print(f"✓ Hydra标准配置文件位于 .hydra/ 目录")

    # 1. 获取模型类和配置
    model_type_str = cfg.model.name.lower()
    if model_type_str not in MODEL_REGISTRY:
        raise ValueError(f"不支持的模型类型: {cfg.model.name}。支持的模型: {list(MODEL_REGISTRY.keys())}")
    
    model_class = MODEL_REGISTRY[model_type_str]
    model_config_dict = dict(cfg.model.model)
    
    # 合并训练配置：模型特定配置 + 通用配置
    training_config_dict = OmegaConf.to_container(cfg.model.training, resolve=True)
    training_config_dict.update(OmegaConf.to_container(cfg.training, resolve=True))

    # 2. 将ListConfig转换为tuple，以兼容JAX
    gpu_mesh_shape_tuple = None
    if isinstance(cfg.training.gpu_mesh_shape, (list, ListConfig)):
        gpu_mesh_shape_tuple = tuple(cfg.training.gpu_mesh_shape)

    # 3. 创建运行器实例
    runner = KitaevRunner(
        # 从 system config 获取
        Lx=cfg.system.Lx,
        Ly=cfg.system.Ly,
        Kx=cfg.system.Kx,
        Ky=cfg.system.Ky,
        Kz=cfg.system.Kz,
        J=cfg.system.J,
        hx=cfg.system.h.x,
        hy=cfg.system.h.y,
        hz=cfg.system.h.z,
        Lambda=cfg.system.Lambda,
        reference_energy=cfg.system.reference_energy,
        # 从 model 和 training config 获取
        model_type=cfg.model.name,
        model_class=model_class,
        model_config=model_config_dict,
        training_config=training_config_dict,
        use_model_sharding=cfg.training.use_model_sharding,
        custom_mesh_shape=gpu_mesh_shape_tuple,
        # 输出目录
        output_dir=hydra_output_dir,
        # checkpoint配置
        checkpoint_config=OmegaConf.to_container(cfg.training.checkpoint, resolve=True) if hasattr(cfg.training, 'checkpoint') else None
    )

    # 4. 设置模型并运行
    runner.setup_model()
    runner.run()

    print(f"模拟完成。结果保存在: {hydra_output_dir}")

if __name__ == "__main__":
    main() 