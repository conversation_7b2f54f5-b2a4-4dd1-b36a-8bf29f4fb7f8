# 物理系统参数
# 可以通过命令行覆盖, e.g., python train.py system.Lx=8 system.Ly=8

# 系统尺寸
Lx: 4
Ly: 4

# Kitaev相互作用参数
Kx: 1.0
Ky: 1.0
Kz: 1.0

# Heisenberg相互作用
J: 0.0

# [111]磁场
h:
  x: 0.1
  y: 0.1
  z: 0.1

# 拉格朗日乘子
Lambda: 0.0

# 自旋
spin: 0.5

# 参考能量
reference_energy: -6.396  # 使用一个具体的数值, 或者用 ~ (null) 表示None

# 结果保存根目录
# 简化路径以避免文件系统限制
output_dir: results/L${.Lx}x${.Ly}_K${.Kx}${.Ky}${.Kz}_J${.J}_h${.h.x}${.h.y}${.h.z}_L${.Lambda}