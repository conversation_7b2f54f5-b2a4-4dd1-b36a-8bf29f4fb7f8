# Physics-Informed Graph Transformer (PIGT) Configuration
# 专为Kitaev模型设计的高精度神经量子态架构配置

# 模型基本参数
Lx: 4                           # x方向单元格数量  
Ly: 4                           # y方向单元格数量
d_model: 128                    # 模型嵌入维度（更大的模型容量）
param_dtype: "complex128"       # 复数参数以处理复数波函数

# 图神经网络参数
n_gnn_layers: 3                 # 图神经网络层数（捕获几何结构）
n_heads_gnn: 4                  # GNN中的注意力头数

# Transformer参数  
n_transformer_layers: 6         # Transformer层数（处理长程纠缠）
n_heads_transformer: 8          # Transformer注意力头数

# 特征提取参数
extract_nn_correlations: true   # 是否提取最近邻关联子
extract_nnn_correlations: true  # 是否提取次近邻关联子  
extract_plaquette_features: true # 是否提取plaquette特征
extract_long_range: true        # 是否提取长程关联

# 物理约束参数
apply_symmetry_projection: true  # 是否应用对称性投影
enforce_gauge_invariance: true  # 是否强制规范不变性

# 训练相关参数
use_complex_output: true        # 输出复数对数波函数
normalize_output: true          # 是否归一化输出

# 架构选择标记
_target_: src.models.PIGT.PIGT
