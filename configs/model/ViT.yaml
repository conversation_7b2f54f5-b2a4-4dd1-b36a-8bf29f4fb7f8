# 标准ViT 模型配置
name: ViT

# 模型参数
model:
  num_layers: 8               # 注意力层数
  d_model: 48                 # 嵌入维度 (c)
  heads: 8                    # 多头注意力头数 (h)
  patch_size: 2               # patch大小
  mlp_hidden_dim: 4           # MLP隐藏层维度倍数 (hidden_dim = d_model * mlp_hidden_dim)
  param_dtype: "float64"      # 参数数据类型
  use_symmetries: false       # 是否使用对称性

# 训练参数
training:
  use_model_sharding: true     # 默认启用模型分片

  seed: 0
  learning_rate: 0.03         # 学习率
  n_train: 1                  # 每次退火的训练步数
  n_samples: 4096             # 样本数量 (2**12)
  n_discard_per_chain: 0      # 每条链丢弃的样本数
  chunk_size: 1024            # 批处理大小 (2**10)
  diag_shift: 0.10            # 对角线位移
  grad_clip: 0.75              # 梯度裁剪阈值

  # 热重启余弦退火参数
  n_cycles: 8                 # 热重启周期数
  initial_period: 100         # 初始周期长度
  period_mult: 2.0            # 周期倍数
  max_temperature: 1.0        # 最大温度
  min_temperature: 0.0        # 最小温度 